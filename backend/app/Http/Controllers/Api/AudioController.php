<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\WhisperService;
use App\Services\OpenAIService;
use App\Services\ElevenLabsService;
use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Events\AudioStreamReceived;

class AudioController extends Controller
{
    protected $whisperService;
    protected $openAIService;
    protected $elevenLabsService;

    public function __construct(
        WhisperService $whisperService,
        OpenAIService $openAIService,
        ElevenLabsService $elevenLabsService
    ) {
        $this->whisperService = $whisperService;
        $this->openAIService = $openAIService;
        $this->elevenLabsService = $elevenLabsService;
    }

    /**
     * Process audio: Speech-to-Text -> ChatGPT -> Text-to-Speech
     */
    public function processAudio(Request $request): JsonResponse
    {
        // Increase execution time for audio processing
        set_time_limit(300); // 5 minutes

        try {
            $request->validate([
                'audio' => 'required|file|mimes:wav,mp3,m4a,webm,ogg|max:10240', // 10MB max
                'session_id' => 'nullable|string', // Frontend session ID
            ]);

            // Additional file checks
            $audioFile = $request->file('audio');
            if (!$audioFile->isValid()) {
                throw new \Exception('Geçersiz ses dosyası');
            }

            // Check file size (additional safety)
            if ($audioFile->getSize() > 10 * 1024 * 1024) { // 10MB
                throw new \Exception('Ses dosyası çok büyük (maksimum 10MB)');
            }

            // 1. Save uploaded audio file
            $audioPath = $audioFile->store('audio/uploads', 'public');
            $fullAudioPath = storage_path('app/public/' . $audioPath);

            Log::info('Audio file uploaded', ['path' => $fullAudioPath]);

            // 2. Speech-to-Text with Whisper
            $transcription = $this->whisperService->transcribe($fullAudioPath);
            Log::info('Transcription completed', ['text' => $transcription]);

            // 3. Send to ChatGPT Assistant
            $response = $this->openAIService->sendMessage($transcription);
            Log::info('ChatGPT response received', ['response' => $response]);

            // 4. Text-to-Speech with ElevenLabs
            $audioResponse = $this->elevenLabsService->textToSpeech($response);

            // 5. Save response audio
            $responseAudioPath = 'audio/responses/' . uniqid() . '.mp3';
            Storage::disk('public')->put($responseAudioPath, $audioResponse);
            $audioUrl = Storage::disk('public')->url($responseAudioPath);

            // 6. Sohbet ve mesajları veritabanına kaydet
            $sessionId = $request->input('session_id');
            $isNewSession = !$sessionId;

            if (!$sessionId) {
                $sessionId = Str::uuid()->toString();
            }

            Log::info('Audio processing session management', [
                'received_session_id' => $request->input('session_id'),
                'final_session_id' => $sessionId,
                'is_new_session' => $isNewSession
            ]);

            // Sohbeti bul veya oluştur
            $conversation = Conversation::firstOrCreate(
                ['session_id' => $sessionId],
                ['last_message_at' => now()]
            );

            Log::info('Conversation found/created', [
                'conversation_id' => $conversation->id,
                'session_id' => $conversation->session_id,
                'was_recently_created' => $conversation->wasRecentlyCreated
            ]);

            // Kullanıcı mesajını kaydet
            $userMessage = Message::create([
                'conversation_id' => $conversation->id,
                'type' => 'user',
                'content' => $transcription,
                'metadata' => [
                    'audio_duration' => null, // Gerekirse eklenebilir
                    'transcription_time' => microtime(true) - LARAVEL_START,
                ]
            ]);

            // Asistan mesajını kaydet
            $assistantMessage = Message::create([
                'conversation_id' => $conversation->id,
                'type' => 'assistant',
                'content' => $response,
                'audio_url' => $audioUrl,
                'metadata' => [
                    'model' => 'gpt-4',
                    'voice_id' => config('services.elevenlabs.voice_id'),
                ]
            ]);

            // Sohbet başlığını oluştur (ilk mesajsa)
            $conversation->generateTitle();
            $conversation->update(['last_message_at' => now()]);

            // 7. Clean up uploaded file
            Storage::disk('public')->delete($audioPath);

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'transcription' => $transcription,
                'response_text' => $response,
                'audio_url' => $audioUrl,
                'message_ids' => [
                    'user' => $userMessage->id,
                    'assistant' => $assistantMessage->id,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Audio processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Audio processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process audio with WebSocket streaming for faster TTS using Laravel Reverb
     */
    public function processAudioFast(Request $request): JsonResponse
    {
        // Increase execution time for audio processing
        set_time_limit(300); // 5 minutes

        try {
            $request->validate([
                'audio' => 'required|file|mimes:wav,mp3,m4a,ogg,webm|max:10240', // 10MB max
                'session_id' => 'nullable|string|max:255',
            ]);

            $audioFile = $request->file('audio');
            $sessionId = $request->input('session_id', 'default');

            Log::info('Fast audio processing started with Laravel Reverb', [
                'session_id' => $sessionId,
                'file_size' => $audioFile->getSize(),
                'mime_type' => $audioFile->getMimeType()
            ]);

            // 1. Save uploaded audio file
            $audioPath = $audioFile->store('audio/uploads', 'public');
            $fullAudioPath = storage_path('app/public/' . $audioPath);

            Log::info('Audio file uploaded', ['path' => $fullAudioPath]);

            // 2. Speech-to-Text with Whisper
            $transcription = $this->whisperService->transcribe($fullAudioPath);
            Log::info('Transcription completed', ['text' => $transcription]);

            // 3. Send to ChatGPT Assistant
            $response = $this->openAIService->sendMessage($transcription);
            Log::info('ChatGPT response received', ['response' => $response]);

            // 4. Text-to-Speech with ElevenLabs WebSocket streaming + Laravel Reverb broadcasting
            $audioResponse = $this->elevenLabsService->textToSpeechFast($response, null, $sessionId);

            // 5. Save response audio
            $responseAudioPath = 'audio/responses/' . uniqid() . '.mp3';
            Storage::disk('public')->put($responseAudioPath, $audioResponse);
            $audioUrl = Storage::disk('public')->url($responseAudioPath);

            Log::info('Fast audio processing completed with Reverb integration', [
                'transcription' => $transcription,
                'response' => $response,
                'audio_url' => $audioUrl,
                'session_id' => $sessionId
            ]);

            // 6. Save conversation and messages
            $conversation = Conversation::firstOrCreate(
                ['session_id' => $sessionId],
                ['last_message_at' => now()]
            );

            // Save user message
            Message::create([
                'conversation_id' => $conversation->id,
                'content' => $transcription,
                'is_user' => true,
                'audio_path' => $audioPath,
            ]);

            // Save assistant response
            Message::create([
                'conversation_id' => $conversation->id,
                'content' => $response,
                'is_user' => false,
                'audio_path' => $responseAudioPath,
            ]);

            // Update conversation timestamp
            $conversation->update(['last_message_at' => now()]);

            // Clean up uploaded file
            if (file_exists($fullAudioPath)) {
                unlink($fullAudioPath);
            }

            return response()->json([
                'success' => true,
                'transcription' => $transcription,
                'response' => $response,
                'audio_url' => $audioUrl,
                'session_id' => $sessionId,
                'method' => 'laravel_reverb_websocket_streaming'
            ]);

        } catch (\Exception $e) {
            Log::error('Fast audio processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'session_id' => $sessionId ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Fast audio processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process audio with real-time chunk streaming - NO FILE SAVING
     * Audio chunks are streamed directly via WebSocket, no MP3 file created
     */
    public function processAudioStreamLive(Request $request): JsonResponse
    {
        set_time_limit(300);

        try {
            $request->validate([
                'audio' => 'required|file|mimes:wav,mp3,m4a,ogg,webm|max:10240',
                'session_id' => 'required|string|max:255',
            ]);

            $audioFile = $request->file('audio');
            $sessionId = $request->input('session_id');

            Log::info('Live audio chunk streaming started', [
                'session_id' => $sessionId,
                'file_size' => $audioFile->getSize(),
            ]);

            // 1. Save uploaded audio file temporarily
            $audioPath = $audioFile->store('audio/uploads', 'public');
            $fullAudioPath = storage_path('app/public/' . $audioPath);

            // 2. Speech-to-Text with Whisper
            $transcription = $this->whisperService->transcribe($fullAudioPath);
            Log::info('Transcription completed', ['text' => $transcription]);

            // 3. Send to ChatGPT Assistant
            $response = $this->openAIService->sendMessage($transcription);
            Log::info('ChatGPT response received', ['response' => $response]);

            // 4. Start streaming immediately - NO FILE SAVING
            // Audio chunks will be sent via WebSocket as they arrive
            $this->elevenLabsService->streamAudioChunksLive($response, $sessionId);

            // 5. Save conversation and messages (without audio file)
            $conversation = Conversation::firstOrCreate(
                ['session_id' => $sessionId],
                ['last_message_at' => now()]
            );

            Message::create([
                'conversation_id' => $conversation->id,
                'content' => $transcription,
                'is_user' => true,
                'audio_path' => $audioPath,
            ]);

            Message::create([
                'conversation_id' => $conversation->id,
                'content' => $response,
                'is_user' => false,
                'audio_path' => null, // No file saved - streamed live
            ]);

            $conversation->update(['last_message_at' => now()]);

            // Clean up uploaded file
            if (file_exists($fullAudioPath)) {
                unlink($fullAudioPath);
            }

            return response()->json([
                'success' => true,
                'transcription' => $transcription,
                'response' => $response,
                'session_id' => $sessionId,
                'method' => 'live_chunk_streaming',
                'streaming_channel' => "audio.{$sessionId}",
                'message' => 'Audio chunks are being streamed live via WebSocket'
            ]);

        } catch (\Exception $e) {
            Log::error('Live audio chunk streaming failed', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Live audio chunk streaming failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process audio with real-time streaming using Laravel Reverb
     * This method still saves the final MP3 file for backup
     */
    public function processAudioStream(Request $request): JsonResponse
    {
        set_time_limit(300);

        try {
            $request->validate([
                'audio' => 'required|file|mimes:wav,mp3,m4a,ogg,webm|max:10240',
                'session_id' => 'required|string|max:255',
            ]);

            $audioFile = $request->file('audio');
            $sessionId = $request->input('session_id');

            Log::info('Real-time audio streaming started', [
                'session_id' => $sessionId,
                'file_size' => $audioFile->getSize(),
            ]);

            // 1. Save uploaded audio file
            $audioPath = $audioFile->store('audio/uploads', 'public');
            $fullAudioPath = storage_path('app/public/' . $audioPath);

            // 2. Speech-to-Text with Whisper
            $transcription = $this->whisperService->transcribe($fullAudioPath);
            Log::info('Transcription completed', ['text' => $transcription]);

            // 3. Send to ChatGPT Assistant
            $response = $this->openAIService->sendMessage($transcription);
            Log::info('ChatGPT response received', ['response' => $response]);

            // 4. Stream TTS with real-time broadcasting to Laravel Reverb
            $audioResponse = $this->elevenLabsService->textToSpeechStreamWithBroadcast(
                $response,
                $sessionId
            );

            // 5. Save response audio (for backup/history)
            $responseAudioPath = 'audio/responses/' . uniqid() . '.mp3';
            Storage::disk('public')->put($responseAudioPath, $audioResponse);
            $audioUrl = Storage::disk('public')->url($responseAudioPath);

            // 6. Save conversation and messages
            $conversation = Conversation::firstOrCreate(
                ['session_id' => $sessionId],
                ['last_message_at' => now()]
            );

            Message::create([
                'conversation_id' => $conversation->id,
                'content' => $transcription,
                'is_user' => true,
                'audio_path' => $audioPath,
            ]);

            Message::create([
                'conversation_id' => $conversation->id,
                'content' => $response,
                'is_user' => false,
                'audio_path' => $responseAudioPath,
            ]);

            $conversation->update(['last_message_at' => now()]);

            // Clean up uploaded file
            if (file_exists($fullAudioPath)) {
                unlink($fullAudioPath);
            }

            return response()->json([
                'success' => true,
                'transcription' => $transcription,
                'response' => $response,
                'audio_url' => $audioUrl,
                'session_id' => $sessionId,
                'method' => 'real_time_streaming_with_reverb'
            ]);

        } catch (\Exception $e) {
            Log::error('Real-time audio streaming failed', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Real-time audio streaming failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Stream audio processing for real-time updates
     */
    public function streamAudio(Request $request): JsonResponse
    {
        // Increase execution time for audio processing
        set_time_limit(300); // 5 minutes

        try {
            $request->validate([
                'audio_chunk' => 'required|string', // Base64 encoded audio chunk
                'session_id' => 'required|string',
                'is_final' => 'required|boolean',
            ]);

            $sessionId = $request->input('session_id');
            $audioChunk = base64_decode($request->input('audio_chunk'));
            $isFinal = $request->boolean('is_final');

            // Geçici klasörü oluştur (varsa hata vermez)
            $tempDir = storage_path('app/temp/');
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0777, true);
            }

            // Session'a özel geçici dosya
            $bufferPath = $tempDir . $sessionId . '_buffer.webm';
            file_put_contents($bufferPath, $audioChunk, FILE_APPEND);

            $transcription = null;
            $responseText = null;
            $audioUrl = null;

            if ($isFinal) {
                // 1. Tüm chunk'ları birleştirip transcribe et
                $transcription = $this->whisperService->transcribe($bufferPath);

                // 2. ChatGPT'ye gönder
                $responseText = $this->openAIService->sendMessage($transcription);

                // 3. TTS ile ses üret
                $audioResponse = $this->elevenLabsService->textToSpeech($responseText);
                $responseAudioPath = 'audio/responses/' . uniqid() . '.mp3';
                \Illuminate\Support\Facades\Storage::disk('public')->put($responseAudioPath, $audioResponse);
                $audioUrl = \Illuminate\Support\Facades\Storage::disk('public')->url($responseAudioPath);

                // 4. Sohbet ve mesajları kaydet
                $conversation = \App\Models\Conversation::firstOrCreate(
                    ['session_id' => $sessionId],
                    ['last_message_at' => now()]
                );
                \App\Models\Message::create([
                    'conversation_id' => $conversation->id,
                    'type' => 'user',
                    'content' => $transcription,
                    'metadata' => [
                        'transcription_time' => microtime(true) - LARAVEL_START,
                    ]
                ]);
                \App\Models\Message::create([
                    'conversation_id' => $conversation->id,
                    'type' => 'assistant',
                    'content' => $responseText,
                    'audio_url' => $audioUrl,
                    'metadata' => [
                        'model' => 'gpt-4',
                        'voice_id' => config('services.elevenlabs.voice_id'),
                    ]
                ]);
                $conversation->update(['last_message_at' => now()]);
                $conversation->generateTitle();

                // Geçici buffer dosyasını sil
                if (file_exists($bufferPath)) {
                    unlink($bufferPath);
                }
            } else {
                // Sadece son chunk
                return response()->json([
                    'success' => true,
                    'message' => 'Chunk received successfully',
                    'session_id' => $sessionId
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Audio streaming failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'error' => 'Audio streaming failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}