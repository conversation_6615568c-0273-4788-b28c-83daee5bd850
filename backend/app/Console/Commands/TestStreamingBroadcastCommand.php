<?php

namespace App\Console\Commands;

use App\Services\OpenAIRealtimeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestStreamingBroadcastCommand extends Command
{
    protected $signature = 'test:streaming-broadcast {message?}';
    protected $description = 'Test the OpenAI streaming service with broadcast-only mode';

    public function handle()
    {
        $message = $this->argument('message') ?? 'Merhaba, nasılsın?';
        $service = new OpenAIRealtimeService();
        $sessionId = $service->generateSessionId();

        $this->info('Testing OpenAI Streaming Service (Broadcast Only)');
        $this->info("Message: {$message}");
        $this->info("Session ID: {$sessionId}");
        $this->line('');

        try {
            $this->info('Starting streaming with broadcast...');
            $fullResponse = $service->streamChatBroadcastOnly($message, $sessionId);
            
            $this->info('✅ Streaming completed successfully!');
            $this->info("Full response: {$fullResponse}");
            $this->line('');
            $this->info('Check your WebSocket client or Laravel Reverb logs to see the broadcasted chunks.');

        } catch (\Exception $e) {
            $this->error('❌ Streaming test failed: ' . $e->getMessage());
            Log::error('Streaming broadcast test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }
}
