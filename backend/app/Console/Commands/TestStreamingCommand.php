<?php

namespace App\Console\Commands;

use App\Services\OpenAIRealtimeService;
use App\Jobs\BroadcastChatChunk;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestStreamingCommand extends Command
{
    protected $signature = 'test:streaming {message?}';
    protected $description = 'Test the new OpenAI streaming service with broadcasting';

    public function handle()
    {
        $message = $this->argument('message') ?? 'Hello, how are you?';
        $service = new OpenAIRealtimeService();
        $sessionId = $service->generateSessionId();

        $this->info('Testing OpenAI Streaming Service');
        $this->info("Message: {$message}");
        $this->info("Session ID: {$sessionId}");
        $this->line('');

        try {
            $this->info('Testing simple message sending...');
            $response = $service->sendMessage($message);
            $this->info("Response: {$response}");
            $this->line('');

            $this->info('Testing broadcast job dispatch...');
            BroadcastChatChunk::dispatch($sessionId, 'Test chunk', 'text', [
                'model' => 'gpt-4o',
                'timestamp' => now()->toISOString()
            ]);
            $this->info('Broadcast job dispatched successfully');
            $this->line('');

            $this->info('Testing session ID validation...');
            $isValid = $service->isValidSessionId($sessionId);
            $this->info("Session ID valid: " . ($isValid ? 'Yes' : 'No'));

            $this->info('✅ All tests passed!');

        } catch (\Exception $e) {
            $this->error('❌ Test failed: ' . $e->getMessage());
            Log::error('Streaming test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }
}
