<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AudioChunkReceived implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $sessionId;
    public $audioChunk;
    public $chunkSize;
    public $isFinal;
    public $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $sessionId,
        string $audioChunk,
        int $chunkSize,
        bool $isFinal = false
    ) {
        $this->sessionId = $sessionId;
        $this->audioChunk = $audioChunk;
        $this->chunkSize = $chunkSize;
        $this->isFinal = $isFinal;
        $this->timestamp = now()->toISOString();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('audio.' . $this->sessionId),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'session_id' => $this->sessionId,
            'audio_chunk' => $this->audioChunk,
            'chunk_size' => $this->chunkSize,
            'is_final' => $this->isFinal,
            'timestamp' => $this->timestamp,
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'AudioChunkReceived';
    }
}
