<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use React\Socket\Connector;
use React\EventLoop\Loop;
use React\Stream\WritableResourceStream;
use React\Socket\ConnectionInterface;

class ElevenLabsWebSocketClient
{
    protected $apiKey;
    protected $voiceId;
    protected $loop;
    protected $connector;

    public function __construct(string $apiKey, string $voiceId)
    {
        $this->apiKey = $apiKey;
        $this->voiceId = $voiceId;
        $this->loop = Loop::get();
        $this->connector = new Connector($this->loop);
    }

    /**
     * Connect to ElevenLabs WebSocket and stream text-to-speech
     */
    public function streamTextToSpeech(
        string $text, 
        callable $onAudioChunk = null, 
        array $voiceSettings = null,
        string $sessionId = null
    ): \Generator {
        $voiceSettings = $voiceSettings ?? [
            'stability' => 0.5,
            'similarity_boost' => 0.5,
            'style' => 0.0,
            'use_speaker_boost' => true,
        ];

        // Build WebSocket URL with query parameters
        $queryParams = [
            'model_id' => 'eleven_multilingual_v2',
            'enable_logging' => true,
            'output_format' => 'mp3_44100_128',
            'inactivity_timeout' => 30,
        ];

        $wsUrl = "wss://api.elevenlabs.io/v1/text-to-speech/{$this->voiceId}/stream-input?" . 
                 http_build_query($queryParams);

        $audioChunks = [];
        $isComplete = false;
        $error = null;

        // Create WebSocket headers
        $headers = [
            'xi-api-key' => $this->apiKey,
            'User-Agent' => 'Laravel-ElevenLabs-Client/1.0',
        ];

        Log::info('Connecting to ElevenLabs WebSocket', [
            'url' => $wsUrl,
            'voice_id' => $this->voiceId,
            'session_id' => $sessionId,
        ]);

        try {
            // Create TCP connection first
            $this->connector->connect($wsUrl, [
                'timeout' => 30,
                'tls' => [
                    'verify_peer' => true,
                    'verify_peer_name' => true,
                ]
            ])->then(
                function (ConnectionInterface $connection) use (
                    $text, $voiceSettings, &$audioChunks, &$isComplete, &$error, $onAudioChunk, $sessionId
                ) {
                    // Send WebSocket handshake
                    $this->sendWebSocketHandshake($connection, $headers);

                    // Initialize connection with voice settings
                    $initMessage = json_encode([
                        'text' => ' ',
                        'voice_settings' => $voiceSettings,
                        'xi_api_key' => $this->apiKey,
                    ]);

                    $this->sendWebSocketFrame($connection, $initMessage);

                    // Send the actual text
                    $textMessage = json_encode([
                        'text' => $text,
                        'try_trigger_generation' => true,
                    ]);

                    $this->sendWebSocketFrame($connection, $textMessage);

                    // Send end signal
                    $endMessage = json_encode(['text' => '']);
                    $this->sendWebSocketFrame($connection, $endMessage);

                    // Handle incoming data
                    $connection->on('data', function ($data) use (
                        &$audioChunks, &$isComplete, $onAudioChunk, $sessionId
                    ) {
                        $frames = $this->parseWebSocketFrames($data);
                        
                        foreach ($frames as $frame) {
                            $messageData = json_decode($frame, true);
                            
                            if (isset($messageData['audio'])) {
                                $audioData = base64_decode($messageData['audio']);
                                $audioChunks[] = $audioData;

                                // Broadcast to Laravel Reverb if session ID provided
                                if ($sessionId && $onAudioChunk) {
                                    $onAudioChunk($audioData, $messageData, $sessionId);
                                }

                                Log::debug('Audio chunk received', [
                                    'size' => strlen($audioData),
                                    'session_id' => $sessionId,
                                ]);
                            }

                            if (isset($messageData['isFinal']) && $messageData['isFinal']) {
                                $isComplete = true;
                                Log::info('ElevenLabs streaming completed', [
                                    'total_chunks' => count($audioChunks),
                                    'session_id' => $sessionId,
                                ]);
                            }
                        }
                    });

                    $connection->on('close', function () use (&$isComplete) {
                        $isComplete = true;
                    });

                    $connection->on('error', function (\Exception $e) use (&$error, &$isComplete) {
                        $error = $e;
                        $isComplete = true;
                    });
                },
                function (\Exception $e) use (&$error, &$isComplete) {
                    $error = $e;
                    $isComplete = true;
                }
            );

            // Run event loop until completion
            $timeout = 60; // 60 seconds timeout
            $startTime = time();

            while (!$isComplete && (time() - $startTime) < $timeout) {
                $this->loop->tick();
                usleep(10000); // 10ms delay
            }

            if (!$isComplete) {
                throw new \Exception('WebSocket streaming timed out');
            }

            if ($error) {
                Log::error('ElevenLabs WebSocket streaming failed', [
                    'error' => $error->getMessage(),
                    'session_id' => $sessionId,
                ]);
                throw new \Exception('WebSocket streaming failed: ' . $error->getMessage());
            }

            // Yield audio chunks
            foreach ($audioChunks as $chunk) {
                yield $chunk;
            }

        } catch (\Exception $e) {
            Log::error('ElevenLabs WebSocket connection failed', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId,
            ]);
            throw $e;
        }
    }

    /**
     * Send WebSocket handshake
     */
    protected function sendWebSocketHandshake(ConnectionInterface $connection, array $headers): void
    {
        $key = base64_encode(random_bytes(16));
        
        $handshake = "GET /v1/text-to-speech/{$this->voiceId}/stream-input HTTP/1.1\r\n";
        $handshake .= "Host: api.elevenlabs.io\r\n";
        $handshake .= "Upgrade: websocket\r\n";
        $handshake .= "Connection: Upgrade\r\n";
        $handshake .= "Sec-WebSocket-Key: {$key}\r\n";
        $handshake .= "Sec-WebSocket-Version: 13\r\n";
        
        foreach ($headers as $name => $value) {
            $handshake .= "{$name}: {$value}\r\n";
        }
        
        $handshake .= "\r\n";
        
        $connection->write($handshake);
    }

    /**
     * Send WebSocket frame
     */
    protected function sendWebSocketFrame(ConnectionInterface $connection, string $data): void
    {
        $length = strlen($data);
        $frame = chr(0x81); // Text frame, final fragment

        if ($length < 126) {
            $frame .= chr($length);
        } elseif ($length < 65536) {
            $frame .= chr(126) . pack('n', $length);
        } else {
            $frame .= chr(127) . pack('J', $length);
        }

        // Add masking key (required for client frames)
        $mask = random_bytes(4);
        $frame .= $mask;

        // Mask the data
        for ($i = 0; $i < $length; $i++) {
            $frame .= $data[$i] ^ $mask[$i % 4];
        }

        $connection->write($frame);
    }

    /**
     * Parse WebSocket frames from raw data
     */
    protected function parseWebSocketFrames(string $data): array
    {
        $frames = [];
        $offset = 0;
        $dataLength = strlen($data);

        while ($offset < $dataLength) {
            if ($dataLength - $offset < 2) break;

            $firstByte = ord($data[$offset]);
            $secondByte = ord($data[$offset + 1]);

            $fin = ($firstByte & 0x80) === 0x80;
            $opcode = $firstByte & 0x0F;
            $masked = ($secondByte & 0x80) === 0x80;
            $payloadLength = $secondByte & 0x7F;

            $offset += 2;

            if ($payloadLength === 126) {
                if ($dataLength - $offset < 2) break;
                $payloadLength = unpack('n', substr($data, $offset, 2))[1];
                $offset += 2;
            } elseif ($payloadLength === 127) {
                if ($dataLength - $offset < 8) break;
                $payloadLength = unpack('J', substr($data, $offset, 8))[1];
                $offset += 8;
            }

            if ($masked) {
                if ($dataLength - $offset < 4) break;
                $mask = substr($data, $offset, 4);
                $offset += 4;
            }

            if ($dataLength - $offset < $payloadLength) break;

            $payload = substr($data, $offset, $payloadLength);
            $offset += $payloadLength;

            if ($masked) {
                for ($i = 0; $i < $payloadLength; $i++) {
                    $payload[$i] = $payload[$i] ^ $mask[$i % 4];
                }
            }

            if ($opcode === 0x1) { // Text frame
                $frames[] = $payload;
            }
        }

        return $frames;
    }
}
