<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class OpenAIService
{
    protected $client;
    protected $apiKey;
    protected $assistantId;
    protected $realtimeService;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => 120, // 2 minutes timeout
            'connect_timeout' => 30, // 30 seconds connection timeout
        ]);
        $this->apiKey = env('OPENAI_API_KEY');
        $this->assistantId = env('OPENAI_ASSISTANT_ID');

        if (empty($this->apiKey)) {
            throw new \Exception('OpenAI API key is not configured');
        }
    }

    /**
     * Get or create OpenAI Realtime Service instance
     */
    public function getRealtimeService(): OpenAIRealtimeService
    {
        if (!$this->realtimeService) {
            $this->realtimeService = new OpenAIRealtimeService();
        }
        return $this->realtimeService;
    }

    /**
     * Connect to OpenAI Realtime API via WebSocket
     */
    public function connectRealtime(): \React\Promise\PromiseInterface
    {
        return $this->getRealtimeService()->connect();
    }

    /**
     * Send real-time text message
     */
    public function sendRealtimeText(string $message): bool
    {
        return $this->getRealtimeService()->sendTextMessage($message);
    }

    /**
     * Send real-time audio data
     */
    public function sendRealtimeAudio(string $audioBase64): bool
    {
        return $this->getRealtimeService()->sendAudioData($audioBase64);
    }

    /**
     * Check if realtime connection is active
     */
    public function isRealtimeConnected(): bool
    {
        return $this->realtimeService && $this->realtimeService->isConnected();
    }

    /**
     * Disconnect from realtime API
     */
    public function disconnectRealtime(): void
    {
        if ($this->realtimeService) {
            $this->realtimeService->disconnect();
        }
    }

    /**
     * Send message to ChatGPT Assistant
     */
    public function sendMessage(string $message): string
    {
        try {
            // If assistant ID is provided, use Assistant API
            if ($this->assistantId) {
                return $this->sendToAssistant($message);
            }
            
            // Otherwise use Chat Completions API
            return $this->sendToChatCompletion($message);

        } catch (\Exception $e) {
            Log::error('OpenAI service error', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Send message using Assistant API
     */
    protected function sendToAssistant(string $message): string
    {
        try {
            // Create a thread
            $threadResponse = $this->client->post('https://api.openai.com/v1/threads', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                    'OpenAI-Beta' => 'assistants=v2',
                ],
                'json' => []
            ]);

            $thread = json_decode($threadResponse->getBody()->getContents(), true);
            $threadId = $thread['id'];

            // Add message to thread
            $this->client->post("https://api.openai.com/v1/threads/{$threadId}/messages", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                    'OpenAI-Beta' => 'assistants=v2',
                ],
                'json' => [
                    'role' => 'user',
                    'content' => $message,
                ]
            ]);

            // Run the assistant
            $runResponse = $this->client->post("https://api.openai.com/v1/threads/{$threadId}/runs", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                    'OpenAI-Beta' => 'assistants=v2',
                ],
                'json' => [
                    'assistant_id' => $this->assistantId,
                ]
            ]);

            $run = json_decode($runResponse->getBody()->getContents(), true);
            $runId = $run['id'];

            // Wait for completion
            $maxAttempts = 30;
            $attempts = 0;
            
            do {
                sleep(1);
                $attempts++;
                
                $statusResponse = $this->client->get("https://api.openai.com/v1/threads/{$threadId}/runs/{$runId}", [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $this->apiKey,
                        'OpenAI-Beta' => 'assistants=v2',
                    ],
                ]);

                $status = json_decode($statusResponse->getBody()->getContents(), true);
                
            } while ($status['status'] === 'in_progress' && $attempts < $maxAttempts);

            if ($status['status'] !== 'completed') {
                throw new \Exception('Assistant run did not complete successfully');
            }

            // Get messages
            $messagesResponse = $this->client->get("https://api.openai.com/v1/threads/{$threadId}/messages", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'OpenAI-Beta' => 'assistants=v2',
                ],
            ]);

            $messages = json_decode($messagesResponse->getBody()->getContents(), true);
            
            // Get the latest assistant message
            foreach ($messages['data'] as $msg) {
                if ($msg['role'] === 'assistant') {
                    return $msg['content'][0]['text']['value'];
                }
            }

            throw new \Exception('No assistant response found');

        } catch (RequestException $e) {
            Log::error('OpenAI Assistant API request failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            throw new \Exception('ChatGPT Assistant request failed');
        }
    }

    /**
     * Send message with image using Vision API (şimdilik yorumda)
     */
    /*
    public function sendMessageWithImage(string $message, string $imageData, string $mimeType): string
    {
        try {
            $messages = [
                [
                    'role' => 'system',
                    'content' => 'Sen yardımcı bir asistansın. Türkçe konuşuyorsun ve kullanıcılara yardım ediyorsun. Görselleri analiz edebilir ve hakkında detaylı bilgi verebilirsin.'
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $message ?: 'Bu görseli analiz et ve ne gördüğünü açıkla.'
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => "data:{$mimeType};base64,{$imageData}"
                            ]
                        ]
                    ]
                ]
            ];

            $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-4o', // GPT-4o görsel analizi destekliyor
                    'messages' => $messages,
                    'max_tokens' => 1000,
                    'temperature' => 0.7,
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!isset($data['choices'][0]['message']['content'])) {
                throw new \Exception('Invalid response from ChatGPT Vision API');
            }

            return $data['choices'][0]['message']['content'];

        } catch (\Exception $e) {
            Log::error('ChatGPT Vision API failed', [
                'error' => $e->getMessage(),
                'message' => $message
            ]);
            throw new \Exception('ChatGPT Vision API request failed: ' . $e->getMessage());
        }
    }
    */

    /**
     * Send message using Chat Completions API with streaming support
     */
    public function sendMessageStream(string $message, ?callable $onChunk = null): string
    {
        try {
            $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-4',
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => 'Sen yardımcı bir asistansın. Türkçe konuşuyorsun ve kullanıcılara yardım ediyorsun.'
                        ],
                        [
                            'role' => 'user',
                            'content' => $message,
                        ],
                    ],
                    'max_tokens' => 1000,
                    'temperature' => 0.7,
                    'stream' => true,
                ],
                'stream' => true,
            ]);

            $fullResponse = '';
            $body = $response->getBody();

            while (!$body->eof()) {
                $chunk = $body->read(1024);
                if ($chunk) {
                    $lines = explode("\n", $chunk);
                    foreach ($lines as $line) {
                        if (strpos($line, 'data: ') === 0) {
                            $data = substr($line, 6);
                            if ($data === '[DONE]') {
                                break 2;
                            }

                            $json = json_decode($data, true);
                            if ($json && isset($json['choices'][0]['delta']['content'])) {
                                $content = $json['choices'][0]['delta']['content'];
                                $fullResponse .= $content;

                                if ($onChunk) {
                                    $onChunk($content);
                                }
                            }
                        }
                    }
                }
            }

            return $fullResponse;

        } catch (\Exception $e) {
            Log::error('OpenAI streaming request failed', [
                'error' => $e->getMessage(),
                'message' => $message
            ]);
            throw new \Exception('ChatGPT streaming request failed');
        }
    }

    /**
     * Send message using Chat Completions API
     */
    protected function sendToChatCompletion(string $message): string
    {
        try {
            $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-4',
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => 'Sen yardımcı bir asistansın. Türkçe konuşuyorsun ve kullanıcılara yardım ediyorsun.'
                        ],
                        [
                            'role' => 'user',
                            'content' => $message,
                        ],
                    ],
                    'max_tokens' => 1000,
                    'temperature' => 0.7,
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!isset($data['choices'][0]['message']['content'])) {
                throw new \Exception('Invalid response from ChatGPT API');
            }

            return $data['choices'][0]['message']['content'];

        } catch (RequestException $e) {
            Log::error('OpenAI Chat API request failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            throw new \Exception('ChatGPT request failed');
        }
    }
}
