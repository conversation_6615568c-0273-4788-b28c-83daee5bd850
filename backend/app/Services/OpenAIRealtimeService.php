<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use OpenAI\Laravel\Facades\OpenAI;
use App\Events\ChatStreamChunk;
use App\Jobs\BroadcastChatChunk;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Exception;

class OpenAIRealtimeService
{
    protected $apiKey;

    public function __construct()
    {
        $this->apiKey = env('OPENAI_API_KEY');
    }

    /**
     * Stream chat completion using OpenAI PHP SDK
     */
    public function streamChat(string $message, string $sessionId, ?callable $onChunk = null): StreamedResponse
    {
        return new StreamedResponse(function () use ($message, $sessionId, $onChunk) {
            try {
                Log::info('Starting OpenAI chat stream', [
                    'session_id' => $sessionId,
                    'message_length' => strlen($message)
                ]);

                $stream = OpenAI::chat()->createStreamed([
                    'model' => 'gpt-4o',
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => 'Sen yardımcı bir asistansın. Türkçe konuşuyorsun ve kullanıcılara yardım ediyorsun.'
                        ],
                        [
                            'role' => 'user',
                            'content' => $message,
                        ],
                    ],
                    'max_tokens' => 1000,
                    'temperature' => 0.7,
                    'stream' => true,
                ]);

                $fullResponse = '';

                foreach ($stream as $response) {
                    $chunk = $response->choices[0]->delta->content ?? '';

                    if (!empty($chunk)) {
                        $fullResponse .= $chunk;

                        // Dispatch broadcast event via queue
                        BroadcastChatChunk::dispatch($sessionId, $chunk, 'text', [
                            'model' => 'gpt-4o',
                            'timestamp' => now()->toISOString()
                        ]);

                        // Call custom callback if provided
                        if ($onChunk) {
                            call_user_func($onChunk, $chunk);
                        }

                        // For HTTP streaming, output the chunk
                        echo "data: " . json_encode(['chunk' => $chunk, 'type' => 'text']) . "\n\n";
                        ob_flush();
                        flush();
                    }
                }

                // Send completion event
                BroadcastChatChunk::dispatch($sessionId, '', 'complete', [
                    'full_response' => $fullResponse,
                    'model' => 'gpt-4o',
                    'timestamp' => now()->toISOString()
                ]);

                echo "data: " . json_encode(['type' => 'complete', 'full_response' => $fullResponse]) . "\n\n";
                ob_flush();
                flush();

                Log::info('OpenAI chat stream completed', [
                    'session_id' => $sessionId,
                    'response_length' => strlen($fullResponse)
                ]);

            } catch (\Exception $e) {
                Log::error('OpenAI chat stream failed', [
                    'session_id' => $sessionId,
                    'error' => $e->getMessage()
                ]);

                // Send error event
                BroadcastChatChunk::dispatch($sessionId, '', 'error', [
                    'error' => $e->getMessage(),
                    'timestamp' => now()->toISOString()
                ]);

                echo "data: " . json_encode(['type' => 'error', 'error' => $e->getMessage()]) . "\n\n";
                ob_flush();
                flush();
            }
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Headers' => 'Cache-Control'
        ]);
    }

    /**
     * Stream chat completion and broadcast only (no HTTP response)
     */
    public function streamChatBroadcastOnly(string $message, string $sessionId): string
    {
        try {
            Log::info('Starting OpenAI chat stream (broadcast only)', [
                'session_id' => $sessionId,
                'message_length' => strlen($message)
            ]);

            $stream = OpenAI::chat()->createStreamed([
                'model' => 'gpt-4o',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Sen yardımcı bir asistansın. Türkçe konuşuyorsun ve kullanıcılara yardım ediyorsun.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $message,
                    ],
                ],
                'max_tokens' => 1000,
                'temperature' => 0.7,
                'stream' => true,
            ]);

            $fullResponse = '';

            foreach ($stream as $response) {
                $chunk = $response->choices[0]->delta->content ?? '';

                if (!empty($chunk)) {
                    $fullResponse .= $chunk;

                    // Dispatch broadcast event via queue
                    BroadcastChatChunk::dispatch($sessionId, $chunk, 'text', [
                        'model' => 'gpt-4o',
                        'timestamp' => now()->toISOString()
                    ]);
                }
            }

            // Send completion event
            BroadcastChatChunk::dispatch($sessionId, '', 'complete', [
                'full_response' => $fullResponse,
                'model' => 'gpt-4o',
                'timestamp' => now()->toISOString()
            ]);

            Log::info('OpenAI chat stream completed (broadcast only)', [
                'session_id' => $sessionId,
                'response_length' => strlen($fullResponse)
            ]);

            return $fullResponse;

        } catch (\Exception $e) {
            Log::error('OpenAI chat stream failed (broadcast only)', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            // Send error event
            BroadcastChatChunk::dispatch($sessionId, '', 'error', [
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ]);

            throw $e;
        }
    }

    /**
     * Send a simple chat message using OpenAI PHP SDK (non-streaming)
     */
    public function sendMessage(string $message): string
    {
        try {
            $response = OpenAI::chat()->create([
                'model' => 'gpt-4o',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Sen yardımcı bir asistansın. Türkçe konuşuyorsun ve kullanıcılara yardım ediyorsun.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $message,
                    ],
                ],
                'max_tokens' => 1000,
                'temperature' => 0.7,
            ]);

            return $response->choices[0]->message->content;

        } catch (\Exception $e) {
            Log::error('OpenAI chat completion failed', [
                'error' => $e->getMessage(),
                'message' => $message
            ]);
            throw $e;
        }
    }

    /**
     * Generate a unique session ID
     */
    public function generateSessionId(): string
    {
        return 'session_' . uniqid() . '_' . time();
    }

    /**
     * Validate session ID format
     */
    public function isValidSessionId(string $sessionId): bool
    {
        return preg_match('/^session_[a-zA-Z0-9_]+$/', $sessionId);
    }
}
