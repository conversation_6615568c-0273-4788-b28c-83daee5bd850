<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Ratchet\Client\WebSocket;
use Ratchet\Client\Connector;
use React\EventLoop\Loop;
use React\Stream\WritableResourceStream;

class ElevenLabsService
{
    protected $client;
    protected $apiKey;
    protected $voiceId;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => 120, // 2 minutes timeout for TTS processing
            'connect_timeout' => 30, // 30 seconds connection timeout
        ]);
        $this->apiKey = env('ELEVENLABS_API_KEY');
        $this->voiceId = env('ELEVENLABS_VOICE_ID', 'pNInz6obpgDQGcFmaJgB'); // Default voice ID

        if (empty($this->apiKey)) {
            throw new \Exception('ElevenLabs API key is not configured');
        }
    }

    /**
     * Convert text to speech using ElevenLabs API
     */
    public function textToSpeech(string $text): string
    {
        try {
            $response = $this->client->post("https://api.elevenlabs.io/v1/text-to-speech/{$this->voiceId}", [
                'headers' => [
                    'Accept' => 'audio/mpeg',
                    'Content-Type' => 'application/json',
                    'xi-api-key' => $this->apiKey,
                ],
                'json' => [
                    'text' => $text,
                    'model_id' => 'eleven_multilingual_v2',
                    'voice_settings' => [
                        'stability' => 0.5,
                        'similarity_boost' => 0.5,
                        'style' => 0.0,
                        'use_speaker_boost' => true,
                    ],
                ],
            ]);

            return $response->getBody()->getContents();

        } catch (RequestException $e) {
            Log::error('ElevenLabs API request failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            throw new \Exception('Text-to-speech conversion failed');
        } catch (\Exception $e) {
            Log::error('ElevenLabs service error', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Convert text to speech using WebSocket streaming for faster response
     */
    public function textToSpeechStream(string $text, callable $onAudioChunk = null, array $voiceSettings = null): \Generator
    {
        $loop = Loop::get();
        $connector = new Connector($loop);

        $voiceSettings = $voiceSettings ?? [
            'stability' => 0.5,
            'similarity_boost' => 0.5,
            'style' => 0.0,
            'use_speaker_boost' => true,
        ];

        $wsUrl = "wss://api.elevenlabs.io/v1/text-to-speech/{$this->voiceId}/stream-input?" . http_build_query([
            'model_id' => 'eleven_multilingual_v2',
            'enable_logging' => true,
            'output_format' => 'mp3_44100_128',
        ]);

        $audioChunks = [];
        $isComplete = false;
        $error = null;

        $connector($wsUrl, [
            'Sec-WebSocket-Protocol' => 'echo-protocol',
            'xi-api-key' => $this->apiKey,
        ])
            ->then(function (WebSocket $conn) use ($text, $voiceSettings, &$audioChunks, &$isComplete, &$error, $onAudioChunk) {

                // Initialize connection with voice settings
                $initMessage = json_encode([
                    'text' => ' ',
                    'voice_settings' => $voiceSettings,
                    'xi_api_key' => $this->apiKey,
                ]);

                $conn->send($initMessage);

                // Send the actual text
                $textMessage = json_encode([
                    'text' => $text,
                    'try_trigger_generation' => true,
                ]);

                $conn->send($textMessage);

                // Send end signal
                $endMessage = json_encode(['text' => '']);
                $conn->send($endMessage);

                $conn->on('message', function ($msg) use (&$audioChunks, &$isComplete, $onAudioChunk) {
                    $data = json_decode($msg->getPayload(), true);

                    if (isset($data['audio'])) {
                        $audioData = base64_decode($data['audio']);
                        $audioChunks[] = $audioData;

                        if ($onAudioChunk) {
                            $onAudioChunk($audioData, $data);
                        }
                    }

                    if (isset($data['isFinal']) && $data['isFinal']) {
                        $isComplete = true;
                    }
                });

                $conn->on('close', function ($code = null, $reason = null) use (&$isComplete) {
                    $isComplete = true;
                });

                $conn->on('error', function (\Exception $e) use (&$error, &$isComplete) {
                    $error = $e;
                    $isComplete = true;
                });

            }, function (\Exception $e) use (&$error, &$isComplete) {
                $error = $e;
                $isComplete = true;
            });

        // Run the event loop until completion
        $timeout = 30; // 30 seconds timeout
        $startTime = time();

        while (!$isComplete && (time() - $startTime) < $timeout) {
            $loop->tick();
            usleep(10000); // 10ms delay to prevent busy waiting
        }

        if (!$isComplete) {
            throw new \Exception('WebSocket streaming timed out');
        }

        if ($error) {
            Log::error('ElevenLabs WebSocket streaming failed', [
                'error' => $error->getMessage(),
            ]);
            throw new \Exception('WebSocket streaming failed: ' . $error->getMessage());
        }

        // Yield audio chunks as they become available
        foreach ($audioChunks as $chunk) {
            yield $chunk;
        }
    }

    /**
     * Convert text to speech using WebSocket streaming and return complete audio
     * This is a drop-in replacement for textToSpeech() but uses WebSocket for better performance
     */
    public function textToSpeechStreamComplete(string $text, array $voiceSettings = null): string
    {
        $audioData = '';

        foreach ($this->textToSpeechStream($text, null, $voiceSettings) as $chunk) {
            $audioData .= $chunk;
        }

        return $audioData;
    }

    /**
     * Convert text to speech using the fastest available method
     * Uses WebSocket streaming by default, falls back to HTTP if WebSocket fails
     */
    public function textToSpeechFast(string $text, array $voiceSettings = null): string
    {
        try {
            // Try WebSocket streaming first for better performance
            return $this->textToSpeechStreamComplete($text, $voiceSettings);
        } catch (\Exception $e) {
            Log::warning('WebSocket streaming failed, falling back to HTTP', [
                'error' => $e->getMessage(),
            ]);

            // Fallback to regular HTTP method
            return $this->textToSpeech($text);
        }
    }

    /**
     * Get available voices
     */
    public function getVoices(): array
    {
        try {
            $response = $this->client->get('https://api.elevenlabs.io/v1/voices', [
                'headers' => [
                    'Accept' => 'application/json',
                    'xi-api-key' => $this->apiKey,
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            return $data['voices'] ?? [];

        } catch (RequestException $e) {
            Log::error('ElevenLabs voices request failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            throw new \Exception('Failed to get voices');
        }
    }
}
