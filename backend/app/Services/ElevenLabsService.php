<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Broadcast;
use React\Socket\Connector;
use React\Stream\WritableResourceStream;
use React\EventLoop\Loop;

class ElevenLabsService
{
    protected $client;
    protected $apiKey;
    protected $voiceId;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => 120, // 2 minutes timeout for TTS processing
            'connect_timeout' => 30, // 30 seconds connection timeout
        ]);
        $this->apiKey = env('ELEVENLABS_API_KEY');
        $this->voiceId = env('ELEVENLABS_VOICE_ID', 'pNInz6obpgDQGcFmaJgB'); // Default voice ID

        if (empty($this->apiKey)) {
            throw new \Exception('ElevenLabs API key is not configured');
        }
    }

    /**
     * Convert text to speech using ElevenLabs API
     */
    public function textToSpeech(string $text): string
    {
        try {
            $response = $this->client->post("https://api.elevenlabs.io/v1/text-to-speech/{$this->voiceId}", [
                'headers' => [
                    'Accept' => 'audio/mpeg',
                    'Content-Type' => 'application/json',
                    'xi-api-key' => $this->apiKey,
                ],
                'json' => [
                    'text' => $text,
                    'model_id' => 'eleven_multilingual_v2',
                    'voice_settings' => [
                        'stability' => 0.5,
                        'similarity_boost' => 0.5,
                        'style' => 0.0,
                        'use_speaker_boost' => true,
                    ],
                ],
            ]);

            return $response->getBody()->getContents();

        } catch (RequestException $e) {
            Log::error('ElevenLabs API request failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            throw new \Exception('Text-to-speech conversion failed');
        } catch (\Exception $e) {
            Log::error('ElevenLabs service error', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Convert text to speech using WebSocket streaming for faster response
     * Now uses Laravel Reverb for better integration
     */
    public function textToSpeechStream(
        string $text,
        callable $onAudioChunk = null,
        array $voiceSettings = null,
        string $sessionId = null
    ): \Generator {
        $client = new ElevenLabsWebSocketClient($this->apiKey, $this->voiceId);

        // Create callback that broadcasts to Laravel Reverb
        $reverbCallback = null;
        if ($sessionId) {
            $reverbCallback = function($audioData, $messageData, $sessionId) use ($onAudioChunk) {
                // Broadcast audio chunk to Laravel Reverb
                try {
                    Broadcast::channel("audio.{$sessionId}")
                        ->send('AudioChunkReceived', [
                            'audio_chunk' => base64_encode($audioData),
                            'chunk_size' => strlen($audioData),
                            'is_final' => $messageData['isFinal'] ?? false,
                            'timestamp' => now()->toISOString(),
                        ]);
                } catch (\Exception $e) {
                    Log::warning('Failed to broadcast audio chunk', [
                        'error' => $e->getMessage(),
                        'session_id' => $sessionId,
                    ]);
                }

                // Call original callback if provided
                if ($onAudioChunk) {
                    $onAudioChunk($audioData, $messageData);
                }
            };
        }

        try {
            foreach ($client->streamTextToSpeech($text, $reverbCallback, $voiceSettings, $sessionId) as $chunk) {
                yield $chunk;
            }
        } catch (\Exception $e) {
            Log::error('ElevenLabs WebSocket streaming failed', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId,
            ]);
            throw $e;
        }
    }

    /**
     * Convert text to speech using WebSocket streaming and return complete audio
     * This is a drop-in replacement for textToSpeech() but uses WebSocket for better performance
     */
    public function textToSpeechStreamComplete(
        string $text,
        array $voiceSettings = null,
        string $sessionId = null
    ): string {
        $audioData = '';

        foreach ($this->textToSpeechStream($text, null, $voiceSettings, $sessionId) as $chunk) {
            $audioData .= $chunk;
        }

        return $audioData;
    }

    /**
     * Convert text to speech using the fastest available method with Laravel Reverb integration
     * Uses WebSocket streaming by default, falls back to HTTP if WebSocket fails
     */
    public function textToSpeechFast(
        string $text,
        array $voiceSettings = null,
        string $sessionId = null
    ): string {
        try {
            // Try WebSocket streaming first for better performance
            return $this->textToSpeechStreamComplete($text, $voiceSettings, $sessionId);
        } catch (\Exception $e) {
            Log::warning('WebSocket streaming failed, falling back to HTTP', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId,
            ]);

            // Fallback to regular HTTP method
            return $this->textToSpeech($text);
        }
    }

    /**
     * Stream text to speech with real-time broadcasting to Laravel Reverb
     * Perfect for real-time audio streaming to frontend clients
     */
    public function textToSpeechStreamWithBroadcast(
        string $text,
        string $sessionId,
        array $voiceSettings = null
    ): string {
        $audioData = '';
        $chunkCount = 0;

        // Broadcast start event
        try {
            Broadcast::channel("audio.{$sessionId}")
                ->send('AudioStreamStarted', [
                    'text' => $text,
                    'session_id' => $sessionId,
                    'timestamp' => now()->toISOString(),
                ]);
        } catch (\Exception $e) {
            Log::warning('Failed to broadcast stream start', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId,
            ]);
        }

        $callback = function($audioChunk, $messageData) use ($sessionId, &$chunkCount) {
            $chunkCount++;

            // This callback is handled by the textToSpeechStream method
            // which already broadcasts via Laravel Reverb
            Log::debug('Processing audio chunk', [
                'chunk_number' => $chunkCount,
                'chunk_size' => strlen($audioChunk),
                'session_id' => $sessionId,
            ]);
        };

        try {
            foreach ($this->textToSpeechStream($text, $callback, $voiceSettings, $sessionId) as $chunk) {
                $audioData .= $chunk;
            }

            // Broadcast completion event
            Broadcast::channel("audio.{$sessionId}")
                ->send('AudioStreamCompleted', [
                    'total_chunks' => $chunkCount,
                    'total_size' => strlen($audioData),
                    'session_id' => $sessionId,
                    'timestamp' => now()->toISOString(),
                ]);

            Log::info('Audio streaming completed', [
                'total_chunks' => $chunkCount,
                'total_size' => strlen($audioData),
                'session_id' => $sessionId,
            ]);

        } catch (\Exception $e) {
            // Broadcast error event
            try {
                Broadcast::channel("audio.{$sessionId}")
                    ->send('AudioStreamError', [
                        'error' => $e->getMessage(),
                        'session_id' => $sessionId,
                        'timestamp' => now()->toISOString(),
                    ]);
            } catch (\Exception $broadcastError) {
                Log::error('Failed to broadcast error', [
                    'original_error' => $e->getMessage(),
                    'broadcast_error' => $broadcastError->getMessage(),
                    'session_id' => $sessionId,
                ]);
            }

            throw $e;
        }

        return $audioData;
    }

    /**
     * Get available voices
     */
    public function getVoices(): array
    {
        try {
            $response = $this->client->get('https://api.elevenlabs.io/v1/voices', [
                'headers' => [
                    'Accept' => 'application/json',
                    'xi-api-key' => $this->apiKey,
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            return $data['voices'] ?? [];

        } catch (RequestException $e) {
            Log::error('ElevenLabs voices request failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            throw new \Exception('Failed to get voices');
        }
    }
}
