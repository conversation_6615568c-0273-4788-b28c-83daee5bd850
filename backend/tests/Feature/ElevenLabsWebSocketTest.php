<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\ElevenLabsService;
use App\Services\ElevenLabsWebSocketClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Broadcast;

class ElevenLabsWebSocketTest extends TestCase
{
    /**
     * Test Laravel Reverb WebSocket streaming functionality
     */
    public function test_laravel_reverb_websocket_streaming_works()
    {
        // Skip test if API key is not configured
        if (empty(env('ELEVENLABS_API_KEY'))) {
            $this->markTestSkipped('ElevenLabs API key not configured');
        }

        $service = new ElevenLabsService();
        $testText = "Hello, this is a test of Laravel Reverb WebSocket streaming.";
        $sessionId = 'test_session_' . uniqid();

        try {
            $audioData = $service->textToSpeechStreamComplete($testText, null, $sessionId);

            $this->assertNotEmpty($audioData);
            $this->assertIsString($audioData);

            // Check if it's valid audio data (MP3 should start with ID3 or have MP3 header)
            $this->assertTrue(
                str_starts_with($audioData, 'ID3') ||
                str_contains($audioData, 'LAME') ||
                strlen($audioData) > 1000 // At least some audio data
            );

        } catch (\Exception $e) {
            $this->fail('Laravel Reverb WebSocket streaming failed: ' . $e->getMessage());
        }
    }

    /**
     * Test fast method with fallback and session ID
     */
    public function test_fast_method_with_fallback()
    {
        // Skip test if API key is not configured
        if (empty(env('ELEVENLABS_API_KEY'))) {
            $this->markTestSkipped('ElevenLabs API key not configured');
        }

        $service = new ElevenLabsService();
        $testText = "Testing fast method with fallback and Laravel Reverb.";
        $sessionId = 'test_session_' . uniqid();

        $audioData = $service->textToSpeechFast($testText, null, $sessionId);

        $this->assertNotEmpty($audioData);
        $this->assertIsString($audioData);
    }

    /**
     * Test WebSocket streaming with callback and Laravel Reverb
     */
    public function test_websocket_streaming_with_callback()
    {
        // Skip test if API key is not configured
        if (empty(env('ELEVENLABS_API_KEY'))) {
            $this->markTestSkipped('ElevenLabs API key not configured');
        }

        $service = new ElevenLabsService();
        $testText = "Testing callback functionality with Laravel Reverb.";
        $sessionId = 'test_session_' . uniqid();
        $chunkCount = 0;

        $callback = function($audioChunk, $data) use (&$chunkCount) {
            $chunkCount++;
            $this->assertNotEmpty($audioChunk);
            $this->assertIsArray($data);
        };

        try {
            $chunks = [];
            foreach ($service->textToSpeechStream($testText, $callback, null, $sessionId) as $chunk) {
                $chunks[] = $chunk;
            }

            $this->assertGreaterThan(0, count($chunks));
            $this->assertGreaterThan(0, $chunkCount);

        } catch (\Exception $e) {
            $this->fail('WebSocket streaming with callback failed: ' . $e->getMessage());
        }
    }

    /**
     * Test real-time streaming with broadcasting
     */
    public function test_streaming_with_broadcast()
    {
        // Skip test if API key is not configured
        if (empty(env('ELEVENLABS_API_KEY'))) {
            $this->markTestSkipped('ElevenLabs API key not configured');
        }

        Broadcast::fake();

        $service = new ElevenLabsService();
        $testText = "Testing real-time streaming with Laravel Reverb broadcasting.";
        $sessionId = 'test_session_' . uniqid();

        try {
            $audioData = $service->textToSpeechStreamWithBroadcast($testText, $sessionId);

            $this->assertNotEmpty($audioData);
            $this->assertIsString($audioData);

            // Verify that broadcast events were sent
            Broadcast::assertSent('AudioStreamStarted');
            Broadcast::assertSent('AudioStreamCompleted');

        } catch (\Exception $e) {
            $this->fail('Streaming with broadcast failed: ' . $e->getMessage());
        }
    }

    /**
     * Test live chunk streaming (no file saving)
     */
    public function test_live_chunk_streaming()
    {
        // Skip test if API key is not configured
        if (empty(env('ELEVENLABS_API_KEY'))) {
            $this->markTestSkipped('ElevenLabs API key not configured');
        }

        Broadcast::fake();

        $service = new ElevenLabsService();
        $testText = "Testing live chunk streaming without file saving.";
        $sessionId = 'test_session_live_' . uniqid();

        try {
            // This method doesn't return audio data - it streams live
            $service->streamAudioChunksLive($testText, $sessionId);

            // Verify that broadcast events were sent
            Broadcast::assertSent('AudioStreamStarted');
            Broadcast::assertSent('AudioChunkReceived');
            Broadcast::assertSent('AudioStreamCompleted');

            $this->assertTrue(true); // Test passed if no exception thrown

        } catch (\Exception $e) {
            $this->fail('Live chunk streaming failed: ' . $e->getMessage());
        }
    }
}
