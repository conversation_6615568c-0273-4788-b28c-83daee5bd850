<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\ElevenLabsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;

class ElevenLabsWebSocketTest extends TestCase
{
    /**
     * Test WebSocket streaming functionality
     */
    public function test_websocket_streaming_works()
    {
        // Skip test if API key is not configured
        if (empty(env('ELEVENLABS_API_KEY'))) {
            $this->markTestSkipped('ElevenLabs API key not configured');
        }

        $service = new ElevenLabsService();
        $testText = "Hello, this is a test of WebSocket streaming.";

        try {
            $audioData = $service->textToSpeechStreamComplete($testText);
            
            $this->assertNotEmpty($audioData);
            $this->assertIsString($audioData);
            
            // Check if it's valid audio data (MP3 should start with ID3 or have MP3 header)
            $this->assertTrue(
                str_starts_with($audioData, 'ID3') || 
                str_contains($audioData, 'LAME') ||
                strlen($audioData) > 1000 // At least some audio data
            );
            
        } catch (\Exception $e) {
            $this->fail('WebSocket streaming failed: ' . $e->getMessage());
        }
    }

    /**
     * Test fast method with fallback
     */
    public function test_fast_method_with_fallback()
    {
        // Skip test if API key is not configured
        if (empty(env('ELEVENLABS_API_KEY'))) {
            $this->markTestSkipped('ElevenLabs API key not configured');
        }

        $service = new ElevenLabsService();
        $testText = "Testing fast method with fallback.";

        $audioData = $service->textToSpeechFast($testText);
        
        $this->assertNotEmpty($audioData);
        $this->assertIsString($audioData);
    }

    /**
     * Test WebSocket streaming with callback
     */
    public function test_websocket_streaming_with_callback()
    {
        // Skip test if API key is not configured
        if (empty(env('ELEVENLABS_API_KEY'))) {
            $this->markTestSkipped('ElevenLabs API key not configured');
        }

        $service = new ElevenLabsService();
        $testText = "Testing callback functionality.";
        $chunkCount = 0;

        $callback = function($audioChunk, $data) use (&$chunkCount) {
            $chunkCount++;
            $this->assertNotEmpty($audioChunk);
            $this->assertIsArray($data);
        };

        try {
            $chunks = [];
            foreach ($service->textToSpeechStream($testText, $callback) as $chunk) {
                $chunks[] = $chunk;
            }
            
            $this->assertGreaterThan(0, count($chunks));
            $this->assertGreaterThan(0, $chunkCount);
            
        } catch (\Exception $e) {
            $this->fail('WebSocket streaming with callback failed: ' . $e->getMessage());
        }
    }
}
