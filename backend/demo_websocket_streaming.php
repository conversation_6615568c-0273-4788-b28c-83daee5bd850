<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\ElevenLabsService;
use Illuminate\Support\Facades\Log;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "ElevenLabs WebSocket Streaming Demo\n";
echo "===================================\n\n";

// Check if API key is configured
if (empty($_ENV['ELEVENLABS_API_KEY'])) {
    echo "❌ Error: ELEVENLABS_API_KEY not configured in .env file\n";
    exit(1);
}

try {
    $service = new ElevenLabsService();
    $testText = "Merhaba! Bu, ElevenLabs WebSocket streaming teknolojisinin bir testidir. Bu yöntem, geleneksel HTTP isteklerinden çok daha hızlı ses üretimi sağlar.";

    echo "🎯 Test Text: {$testText}\n\n";

    // Test 1: Regular HTTP method
    echo "📡 Testing regular HTTP method...\n";
    $startTime = microtime(true);
    
    try {
        $httpAudio = $service->textToSpeech($testText);
        $httpTime = microtime(true) - $startTime;
        echo "✅ HTTP method completed in " . round($httpTime, 2) . " seconds\n";
        echo "📊 Audio size: " . number_format(strlen($httpAudio)) . " bytes\n\n";
    } catch (Exception $e) {
        echo "❌ HTTP method failed: " . $e->getMessage() . "\n\n";
    }

    // Test 2: WebSocket streaming method
    echo "🚀 Testing WebSocket streaming method...\n";
    $startTime = microtime(true);
    
    try {
        $wsAudio = $service->textToSpeechStreamComplete($testText);
        $wsTime = microtime(true) - $startTime;
        echo "✅ WebSocket streaming completed in " . round($wsTime, 2) . " seconds\n";
        echo "📊 Audio size: " . number_format(strlen($wsAudio)) . " bytes\n";
        
        if (isset($httpTime)) {
            $improvement = (($httpTime - $wsTime) / $httpTime) * 100;
            echo "⚡ Performance improvement: " . round($improvement, 1) . "%\n\n";
        }
    } catch (Exception $e) {
        echo "❌ WebSocket streaming failed: " . $e->getMessage() . "\n\n";
    }

    // Test 3: WebSocket streaming with real-time callback
    echo "🎵 Testing WebSocket streaming with real-time callback...\n";
    $chunkCount = 0;
    $totalSize = 0;
    
    $callback = function($audioChunk, $data) use (&$chunkCount, &$totalSize) {
        $chunkCount++;
        $totalSize += strlen($audioChunk);
        echo "  📦 Chunk {$chunkCount}: " . number_format(strlen($audioChunk)) . " bytes";
        if (isset($data['isFinal'])) {
            echo " (Final: " . ($data['isFinal'] ? 'Yes' : 'No') . ")";
        }
        echo "\n";
    };

    $startTime = microtime(true);
    
    try {
        $streamAudio = '';
        foreach ($service->textToSpeechStream($testText, $callback) as $chunk) {
            $streamAudio .= $chunk;
        }
        
        $streamTime = microtime(true) - $startTime;
        echo "✅ Streaming with callback completed in " . round($streamTime, 2) . " seconds\n";
        echo "📊 Total chunks: {$chunkCount}\n";
        echo "📊 Total audio size: " . number_format(strlen($streamAudio)) . " bytes\n\n";
        
    } catch (Exception $e) {
        echo "❌ Streaming with callback failed: " . $e->getMessage() . "\n\n";
    }

    // Test 4: Fast method with fallback
    echo "⚡ Testing fast method with automatic fallback...\n";
    $startTime = microtime(true);
    
    try {
        $fastAudio = $service->textToSpeechFast($testText);
        $fastTime = microtime(true) - $startTime;
        echo "✅ Fast method completed in " . round($fastTime, 2) . " seconds\n";
        echo "📊 Audio size: " . number_format(strlen($fastAudio)) . " bytes\n\n";
    } catch (Exception $e) {
        echo "❌ Fast method failed: " . $e->getMessage() . "\n\n";
    }

    echo "🎉 Demo completed successfully!\n";
    echo "\n💡 Usage Tips:\n";
    echo "- Use textToSpeechFast() for best performance with automatic fallback\n";
    echo "- Use textToSpeechStream() for real-time audio chunk processing\n";
    echo "- Use textToSpeechStreamComplete() for complete audio with WebSocket speed\n";
    echo "- Original textToSpeech() method is still available for compatibility\n";

} catch (Exception $e) {
    echo "❌ Demo failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
