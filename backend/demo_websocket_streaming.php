<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\ElevenLabsService;
use Illuminate\Support\Facades\Log;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "ElevenLabs WebSocket Streaming Demo with Laravel Reverb\n";
echo "======================================================\n\n";

// Check if API key is configured
if (empty($_ENV['ELEVENLABS_API_KEY'])) {
    echo "❌ Error: ELEVENLABS_API_KEY not configured in .env file\n";
    exit(1);
}

try {
    $service = new ElevenLabsService();
    $testText = "Merhaba! Bu, ElevenLabs WebSocket streaming teknolojisinin bir testidir. Bu yöntem, geleneksel HTTP isteklerinden çok daha hızlı ses üretimi sağlar.";

    echo "🎯 Test Text: {$testText}\n\n";

    // Test 1: Regular HTTP method
    echo "📡 Testing regular HTTP method...\n";
    $startTime = microtime(true);
    
    try {
        $httpAudio = $service->textToSpeech($testText);
        $httpTime = microtime(true) - $startTime;
        echo "✅ HTTP method completed in " . round($httpTime, 2) . " seconds\n";
        echo "📊 Audio size: " . number_format(strlen($httpAudio)) . " bytes\n\n";
    } catch (Exception $e) {
        echo "❌ HTTP method failed: " . $e->getMessage() . "\n\n";
    }

    // Test 2: Laravel Reverb WebSocket streaming method
    echo "🚀 Testing Laravel Reverb WebSocket streaming method...\n";
    $sessionId = 'demo_session_' . uniqid();
    $startTime = microtime(true);

    try {
        $wsAudio = $service->textToSpeechStreamComplete($testText, null, $sessionId);
        $wsTime = microtime(true) - $startTime;
        echo "✅ Laravel Reverb WebSocket streaming completed in " . round($wsTime, 2) . " seconds\n";
        echo "📊 Audio size: " . number_format(strlen($wsAudio)) . " bytes\n";
        echo "🔗 Session ID: {$sessionId}\n";

        if (isset($httpTime)) {
            $improvement = (($httpTime - $wsTime) / $httpTime) * 100;
            echo "⚡ Performance improvement: " . round($improvement, 1) . "%\n\n";
        }
    } catch (Exception $e) {
        echo "❌ Laravel Reverb WebSocket streaming failed: " . $e->getMessage() . "\n\n";
    }

    // Test 3: Laravel Reverb WebSocket streaming with real-time callback
    echo "🎵 Testing Laravel Reverb streaming with real-time callback...\n";
    $chunkCount = 0;
    $totalSize = 0;
    $sessionId2 = 'demo_session_callback_' . uniqid();

    $callback = function($audioChunk, $data) use (&$chunkCount, &$totalSize) {
        $chunkCount++;
        $totalSize += strlen($audioChunk);
        echo "  📦 Chunk {$chunkCount}: " . number_format(strlen($audioChunk)) . " bytes";
        if (isset($data['isFinal'])) {
            echo " (Final: " . ($data['isFinal'] ? 'Yes' : 'No') . ")";
        }
        echo "\n";
    };

    $startTime = microtime(true);

    try {
        $streamAudio = '';
        foreach ($service->textToSpeechStream($testText, $callback, null, $sessionId2) as $chunk) {
            $streamAudio .= $chunk;
        }

        $streamTime = microtime(true) - $startTime;
        echo "✅ Laravel Reverb streaming with callback completed in " . round($streamTime, 2) . " seconds\n";
        echo "📊 Total chunks: {$chunkCount}\n";
        echo "📊 Total audio size: " . number_format(strlen($streamAudio)) . " bytes\n";
        echo "🔗 Session ID: {$sessionId2}\n\n";

    } catch (Exception $e) {
        echo "❌ Laravel Reverb streaming with callback failed: " . $e->getMessage() . "\n\n";
    }

    // Test 4: Fast method with fallback and Laravel Reverb
    echo "⚡ Testing fast method with automatic fallback and Laravel Reverb...\n";
    $sessionId3 = 'demo_session_fast_' . uniqid();
    $startTime = microtime(true);

    try {
        $fastAudio = $service->textToSpeechFast($testText, null, $sessionId3);
        $fastTime = microtime(true) - $startTime;
        echo "✅ Fast method with Laravel Reverb completed in " . round($fastTime, 2) . " seconds\n";
        echo "📊 Audio size: " . number_format(strlen($fastAudio)) . " bytes\n";
        echo "🔗 Session ID: {$sessionId3}\n\n";
    } catch (Exception $e) {
        echo "❌ Fast method failed: " . $e->getMessage() . "\n\n";
    }

    // Test 5: Real-time streaming with broadcasting
    echo "📡 Testing real-time streaming with Laravel Reverb broadcasting...\n";
    $sessionId4 = 'demo_session_broadcast_' . uniqid();
    $startTime = microtime(true);

    try {
        $broadcastAudio = $service->textToSpeechStreamWithBroadcast($testText, $sessionId4);
        $broadcastTime = microtime(true) - $startTime;
        echo "✅ Real-time streaming with broadcasting completed in " . round($broadcastTime, 2) . " seconds\n";
        echo "📊 Audio size: " . number_format(strlen($broadcastAudio)) . " bytes\n";
        echo "🔗 Session ID: {$sessionId4}\n";
        echo "📡 Broadcasting events sent to Laravel Reverb\n\n";
    } catch (Exception $e) {
        echo "❌ Real-time streaming with broadcasting failed: " . $e->getMessage() . "\n\n";
    }

    echo "🎉 Laravel Reverb WebSocket Streaming Demo completed successfully!\n";
    echo "\n💡 Usage Tips:\n";
    echo "- Use textToSpeechFast() for best performance with automatic fallback and session tracking\n";
    echo "- Use textToSpeechStream() for real-time audio chunk processing with Laravel Reverb\n";
    echo "- Use textToSpeechStreamComplete() for complete audio with WebSocket speed\n";
    echo "- Use textToSpeechStreamWithBroadcast() for real-time broadcasting to frontend clients\n";
    echo "- Original textToSpeech() method is still available for compatibility\n";
    echo "- All methods now support session IDs for Laravel Reverb integration\n";
    echo "- Real-time events are broadcasted to 'audio.{session_id}' channels\n";

} catch (Exception $e) {
    echo "❌ Demo failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
