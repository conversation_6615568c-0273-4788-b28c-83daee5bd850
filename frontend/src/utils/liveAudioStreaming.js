/**
 * Live Audio Streaming Utility for ElevenLabs WebSocket Integration
 * Handles real-time audio chunk streaming via Laravel Reverb
 */

import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

class LiveAudioStreamer {
    constructor() {
        this.audioContext = null;
        this.audioBuffer = [];
        this.isPlaying = false;
        this.sessionId = null;
        this.echo = null;
        this.channel = null;
        
        // Initialize Web Audio API
        this.initAudioContext();
    }

    /**
     * Initialize Web Audio API context
     */
    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.error('Web Audio API not supported:', error);
        }
    }

    /**
     * Start live audio streaming session
     */
    startSession(sessionId) {
        this.sessionId = sessionId;
        this.audioBuffer = [];
        this.isPlaying = false;

        // Initialize Laravel Echo connection
        window.Pusher = Pusher;
        this.echo = new Echo({
            broadcaster: 'reverb',
            wsHost: process.env.NEXT_PUBLIC_REVERB_HOST || window.location.hostname,
            wsPort: Number(process.env.NEXT_PUBLIC_REVERB_PORT) || 8080,
            wssPort: Number(process.env.NEXT_PUBLIC_REVERB_PORT) || 8080,
            forceTLS: (process.env.NEXT_PUBLIC_REVERB_SCHEME || 'http') === 'https',
            disableStats: true,
            enabledTransports: ['ws', 'wss'],
            scheme: process.env.NEXT_PUBLIC_REVERB_SCHEME || 'http',
        });

        // Listen to audio streaming channel
        this.channel = this.echo.channel(`audio.${sessionId}`);
        
        this.channel.listen('AudioStreamStarted', (data) => {
            console.log('🎵 Audio streaming started:', data);
            this.onStreamStarted(data);
        });

        this.channel.listen('AudioChunkReceived', (data) => {
            console.log('📦 Audio chunk received:', data.chunk_number, 'Size:', data.chunk_size);
            this.onAudioChunkReceived(data);
        });

        this.channel.listen('AudioStreamCompleted', (data) => {
            console.log('✅ Audio streaming completed:', data);
            this.onStreamCompleted(data);
        });

        this.channel.listen('AudioStreamError', (data) => {
            console.error('❌ Audio streaming error:', data);
            this.onStreamError(data);
        });

        console.log(`🔗 Connected to live audio streaming channel: audio.${sessionId}`);
    }

    /**
     * Handle stream started event
     */
    onStreamStarted(data) {
        console.log('Starting live audio playback...');
        this.audioBuffer = [];
        this.isPlaying = false;
        
        // Trigger custom event
        window.dispatchEvent(new CustomEvent('audioStreamStarted', { detail: data }));
    }

    /**
     * Handle incoming audio chunk
     */
    async onAudioChunkReceived(data) {
        try {
            // Decode base64 audio chunk
            const audioChunk = atob(data.audio_chunk);
            const audioArray = new Uint8Array(audioChunk.length);
            
            for (let i = 0; i < audioChunk.length; i++) {
                audioArray[i] = audioChunk.charCodeAt(i);
            }

            // Add to buffer
            this.audioBuffer.push(audioArray);

            // Start playing if not already playing
            if (!this.isPlaying && this.audioBuffer.length >= 2) {
                this.startPlayback();
            }

            // Trigger custom event
            window.dispatchEvent(new CustomEvent('audioChunkReceived', { 
                detail: { 
                    chunkNumber: data.chunk_number,
                    chunkSize: data.chunk_size,
                    isFinal: data.is_final
                }
            }));

        } catch (error) {
            console.error('Error processing audio chunk:', error);
        }
    }

    /**
     * Start audio playback from buffer
     */
    async startPlayback() {
        if (this.isPlaying || this.audioBuffer.length === 0) return;

        this.isPlaying = true;
        console.log('🔊 Starting live audio playback...');

        try {
            // Resume audio context if suspended
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            this.playNextChunk();

        } catch (error) {
            console.error('Error starting playback:', error);
            this.isPlaying = false;
        }
    }

    /**
     * Play next audio chunk from buffer
     */
    async playNextChunk() {
        if (this.audioBuffer.length === 0) {
            this.isPlaying = false;
            return;
        }

        try {
            const chunk = this.audioBuffer.shift();
            
            // Decode MP3 chunk
            const audioBuffer = await this.audioContext.decodeAudioData(chunk.buffer);
            
            // Create audio source
            const source = this.audioContext.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(this.audioContext.destination);
            
            // Play chunk
            source.start();
            
            // Schedule next chunk
            source.onended = () => {
                this.playNextChunk();
            };

        } catch (error) {
            console.error('Error playing audio chunk:', error);
            // Continue with next chunk
            setTimeout(() => this.playNextChunk(), 100);
        }
    }

    /**
     * Handle stream completion
     */
    onStreamCompleted(data) {
        console.log('🎉 Live audio streaming completed');
        
        // Play remaining chunks
        if (this.audioBuffer.length > 0 && !this.isPlaying) {
            this.startPlayback();
        }

        // Trigger custom event
        window.dispatchEvent(new CustomEvent('audioStreamCompleted', { detail: data }));
    }

    /**
     * Handle stream error
     */
    onStreamError(data) {
        console.error('Live audio streaming error:', data.error);
        this.stopSession();
        
        // Trigger custom event
        window.dispatchEvent(new CustomEvent('audioStreamError', { detail: data }));
    }

    /**
     * Stop streaming session
     */
    stopSession() {
        if (this.channel) {
            this.echo.leave(`audio.${this.sessionId}`);
            this.channel = null;
        }
        
        if (this.echo) {
            this.echo.disconnect();
            this.echo = null;
        }

        this.audioBuffer = [];
        this.isPlaying = false;
        this.sessionId = null;

        console.log('🔌 Disconnected from live audio streaming');
    }

    /**
     * Send audio for live streaming processing
     */
    async sendAudioForLiveStreaming(audioBlob, sessionId) {
        try {
            const formData = new FormData();
            formData.append('audio', audioBlob, 'recording.webm');
            formData.append('session_id', sessionId);

            const response = await fetch('/api/audio/process-stream-live', {
                method: 'POST',
                body: formData,
            });

            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Live streaming request sent successfully');
                return result;
            } else {
                throw new Error(result.error || 'Live streaming failed');
            }

        } catch (error) {
            console.error('Error sending audio for live streaming:', error);
            throw error;
        }
    }
}

// Export singleton instance
export const liveAudioStreamer = new LiveAudioStreamer();

// Usage example:
/*
import { liveAudioStreamer } from '@/utils/liveAudioStreaming';

// Start session
const sessionId = 'user_session_' + Date.now();
liveAudioStreamer.startSession(sessionId);

// Listen to events
window.addEventListener('audioStreamStarted', (event) => {
    console.log('Stream started:', event.detail);
});

window.addEventListener('audioChunkReceived', (event) => {
    console.log('Chunk received:', event.detail);
});

window.addEventListener('audioStreamCompleted', (event) => {
    console.log('Stream completed:', event.detail);
});

// Send audio for processing
const audioBlob = new Blob([audioData], { type: 'audio/webm' });
await liveAudioStreamer.sendAudioForLiveStreaming(audioBlob, sessionId);

// Stop session when done
liveAudioStreamer.stopSession();
*/
