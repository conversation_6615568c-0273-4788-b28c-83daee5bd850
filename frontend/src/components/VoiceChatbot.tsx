'use client';

import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { getErrorMessage } from '@/utils/errorMessages';
import { startAudioRecording, stopAudioRecording } from '@/utils/audioUtils';
import { playAudio } from '../utils/audio';
import VoicePanel from './VoicePanel';
import ChatPanel from './ChatPanel';
import ConversationHistory from './ConversationHistory';
import './VoiceChatbot.css';
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  text: string;
  audioUrl?: string;
  imageUrls?: string[];
  timestamp: Date;
}

interface ChatPanelProps {
  messages: ChatMessage[];
  isProcessing: boolean;
  onSendMessage: (text: string, images?: File[]) => void;
  onInputFocusChange: (isFocused: boolean) => void;
  streamTranscription: string;
  isStreaming: boolean;
}

const VoiceChatbot = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [spaceKeyPressed, setSpaceKeyPressed] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [currentlyPlayingAudio, setCurrentlyPlayingAudio] = useState<HTMLAudioElement | null>(null);
  const [playedAudioUrls, setPlayedAudioUrls] = useState<Set<string>>(new Set());
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamTranscription, setStreamTranscription] = useState('');

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const wsSessionId = useRef<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const echoRef = useRef<any>(null);
  const streamChunksRef = useRef<Blob[]>([]);

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

  // Reverb websocket ayarlarını .env'den al
  const REVERB_KEY = process.env.NEXT_PUBLIC_REVERB_APP_KEY || '';
  const REVERB_HOST = process.env.NEXT_PUBLIC_REVERB_HOST || window.location.hostname;
  const REVERB_PORT = process.env.NEXT_PUBLIC_REVERB_PORT || 8080;
  const REVERB_SCHEME = process.env.NEXT_PUBLIC_REVERB_SCHEME || 'http';

  // Space ve ESC tuşu event listener'ları
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Input alanı focus'tayken Space tuşunu devre dışı bırak
      if (event.code === 'Space' && !spaceKeyPressed && !isProcessing && !isInputFocused) {
        event.preventDefault();
        setSpaceKeyPressed(true);

        if (isRecording) {
          // Eğer kayıt yapılıyorsa, durdur ve gönder
          stopRecording();
        } else {
          // Eğer kayıt yapılmıyorsa, başlat
          startRecording();
        }
      }

      // ESC tuşu ile kayıt iptal et
      if (event.code === 'Escape' && isRecording) {
        event.preventDefault();
        cancelRecording();
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.code === 'Space' && spaceKeyPressed) {
        event.preventDefault();
        setSpaceKeyPressed(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [spaceKeyPressed, isProcessing, isRecording, isInputFocused]);

  // Mikrofon erişimi ve kayıt başlatma (Safari uyumlu)
  const startRecording = async () => {
    try {
      const result = await startAudioRecording({
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      });

      if (!result.isSupported || !result.mediaRecorder || !result.stream) {
        alert(result.error || 'Ses kaydı desteklenmiyor');
        return;
      }

      streamRef.current = result.stream;
      mediaRecorderRef.current = result.mediaRecorder;
      audioChunksRef.current = [];

      result.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      result.mediaRecorder.onstop = () => {
        processAudio();
      };

      result.mediaRecorder.start();
      setIsRecording(true);

    } catch (error: any) {
      console.error('Mikrofon erişimi hatası:', error);
      alert('Mikrofon erişimi sağlanamadı. Lütfen tarayıcı ayarlarını kontrol edin.');
    }
  };

  // Kayıt durdurma
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      // Stream'i temizle
      stopAudioRecording(mediaRecorderRef.current, streamRef.current);
      streamRef.current = null;
      mediaRecorderRef.current = null;
    }
  };

  // Kayıt iptal etme (ESC tuşu ile)
  const cancelRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      setIsRecording(false);

      // MediaRecorder'ı durdur ama processAudio çağırma
      if (mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.ondataavailable = null; // Event handler'ı kaldır
        mediaRecorderRef.current.onstop = null;
        mediaRecorderRef.current.stop();
      }

      // Stream'i temizle
      stopAudioRecording(mediaRecorderRef.current, streamRef.current);
      streamRef.current = null;
      mediaRecorderRef.current = null;
      audioChunksRef.current = []; // Ses verilerini temizle

      console.log('Ses kaydı iptal edildi');
    }
  };

  // Otomatik ses çalma (sadece bir kez)
  const playAudioAutomatically = async (audioUrl: string) => {
    try {
      // Bu ses daha önce çalındıysa çalma
      if (playedAudioUrls.has(audioUrl)) {
        console.log('Bu ses zaten çalındı, tekrar çalmıyor:', audioUrl);
        return;
      }

      // Eğer başka bir ses çalıyorsa durdur
      if (currentlyPlayingAudio) {
        currentlyPlayingAudio.pause();
        currentlyPlayingAudio.currentTime = 0;
      }

      // Bu ses URL'ini çalınmış olarak işaretle
      setPlayedAudioUrls(prev => new Set(prev).add(audioUrl));

      await playAudio(audioUrl);
      console.log('Ses otomatik çalınıyor:', audioUrl);
    } catch (error) {
      console.error('Otomatik ses çalma hatası:', error);
    }
  };

  // Ses dosyasını işleme
  const processAudio = async () => {
    if (audioChunksRef.current.length === 0) return;

    setIsProcessing(true);

    try {
      // Ses blob'unu oluştur
      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });

      // FormData oluştur
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.webm');
      if (sessionId) {
        formData.append('session_id', sessionId);
      }

      // Kullanıcı mesajını ekle (geçici)
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'user',
        text: 'Ses kaydı işleniyor...',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, userMessage]);

      // Backend'e gönder
      const response = await axios.post(`${API_BASE_URL}/audio/process`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 120000, // 2 dakika timeout (backend ile uyumlu)
      });

      if (response.data.success) {
        // Session ID'yi her zaman güncelle (tutarlılık için)
        if (response.data.session_id) {
          const newSessionId = response.data.session_id;
          console.log('Session ID güncelleniyor:', { current: sessionId, new: newSessionId });

          // Eğer session ID değiştiyse güncelle
          if (sessionId !== newSessionId) {
            setSessionId(newSessionId);
            setRefreshTrigger(prev => prev + 1); // Sohbet listesini yenile
          }
        }

        // Kullanıcı mesajını güncelle
        const updatedUserMessage: ChatMessage = {
          ...userMessage,
          text: response.data.transcription,
        };

        // Asistan yanıtını ekle
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          text: response.data.response_text,
          audioUrl: response.data.audio_url,
          timestamp: new Date(),
        };

        setMessages(prev => [
          ...prev.slice(0, -1), // Son mesajı çıkar
          updatedUserMessage,
          assistantMessage
        ]);

        // Ses varsa otomatik çal
        if (response.data.audio_url) {
          setTimeout(() => {
            playAudioAutomatically(response.data.audio_url);
          }, 500); // 500ms bekle ki UI güncellensin
        }

      } else {
        throw new Error(response.data.error || 'Ses işleme hatası');
      }

    } catch (error: any) {
      console.error('Ses işleme hatası:', error);
      alert(getErrorMessage(error));
    } finally {
      setIsProcessing(false);
    }
  };

  // Seçili sohbet değiştiğinde mesajları yükle
  const handleConversationSelect = (selectedSessionId: string) => {
    console.log('Sohbet seçildi:', selectedSessionId); // DEBUG LOG
    setSessionId(selectedSessionId);
    loadMessages(selectedSessionId);
  };

  // Yeni sohbet başlat
  const handleNewConversation = () => {
    setSessionId(null);
    setMessages([]);
  };

  // Belirli bir sessionId için mesajları yükle
  const loadMessages = async (targetSessionId: string) => {
    console.log('loadMessages çağrıldı:', targetSessionId); // DEBUG LOG
    try {
      const response = await axios.get(`${API_BASE_URL}/conversations/${targetSessionId}/messages`);
      if (response.data.success) {
        const loadedMessages = (response.data.messages || []).map((msg: any) => {
          // Epoch timestamp'ı Date nesnesine çevir
          const date = new Date(msg.timestamp * 1000);

          // Geçersiz tarih kontrolü
          if (isNaN(date.getTime())) {
            console.error('Geçersiz tarih:', msg.timestamp);
            return null;
          }

          return {
            id: msg.id,
            type: msg.type,
            text: msg.text, // Backend'den gelen text alanını kullan
            audioUrl: msg.audioUrl, // Backend'den gelen audioUrl alanını kullan
            imageUrls: msg.imageUrls || [], // Backend'den gelen imageUrls alanını kullan
            timestamp: date,
          };
        })
        .filter((msg: any): msg is ChatMessage => msg !== null); // null değerleri filtrele
        console.log('Yüklenen mesajlar:', loadedMessages); // DEBUG LOG
        setMessages(loadedMessages);
      }
    } catch (e) {
      console.error('Mesaj yükleme hatası:', e);
      // Hata durumunda önceki mesajları koru
    }
  };

  // Metin mesajı gönder
  const sendTextMessage = async (text: string, images?: File[]) => {
    if (!text.trim() && (!images || images.length === 0)) return;

    setIsProcessing(true);

    try {
      // Kullanıcı mesajını ekle (geçici)
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'user',
        text: text,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, userMessage]);

      // FormData oluştur
      const formData = new FormData();
      formData.append('message', text);

      if (sessionId) {
        formData.append('session_id', sessionId);
        console.log('Metin mesajı gönderiliyor, session_id:', sessionId);
      }

      // Görseller varsa ekle
      if (images && images.length > 0) {
        images.forEach((image, index) => {
          formData.append(`images[${index}]`, image);
        });
      }

      // Backend'e gönder
      const response = await axios.post(`${API_BASE_URL}/chat`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 120000, // 2 dakika timeout (backend ile uyumlu)
      });

      if (response.data.success) {
        // Session ID'yi güncelle
        if (response.data.session_id) {
          const newSessionId = response.data.session_id;
          console.log('Metin mesajı session ID güncelleniyor:', { current: sessionId, new: newSessionId });

          if (sessionId !== newSessionId) {
            setSessionId(newSessionId);
            setRefreshTrigger(prev => prev + 1);
          }
        }

        // Kullanıcı mesajını güncelle (eğer gerekirse)
        const updatedUserMessage: ChatMessage = {
          ...userMessage,
          text: response.data.message,
        };

        // Asistan yanıtını ekle
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          text: response.data.response,
          audioUrl: response.data.audio_url,
          timestamp: new Date(),
        };

        setMessages(prev => [
          ...prev.slice(0, -1), // Son mesajı çıkar
          updatedUserMessage,
          assistantMessage
        ]);

        // Ses varsa otomatik çal
        if (response.data.audio_url) {
          setTimeout(() => {
            playAudioAutomatically(response.data.audio_url);
          }, 500);
        }

      } else {
        throw new Error(response.data.error || 'Mesaj gönderme hatası');
      }

    } catch (error: any) {
      console.error('Metin mesajı gönderme hatası:', error);
      alert(getErrorMessage(error));

      // Hata durumunda geçici mesajı kaldır
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setIsProcessing(false);
    }
  };

  // WebSocket ile chunk gönderimi ve event dinleme
  useEffect(() => {
    if (!isStreaming || !sessionId) return;
    // Laravel Echo ile Reverb'e bağlan
    window.Pusher = Pusher;
    const echo = new Echo({
      broadcaster: 'reverb',
      wsHost: REVERB_HOST,
      wsPort: Number(REVERB_PORT),
      wssPort: Number(REVERB_PORT),
      forceTLS: REVERB_SCHEME === 'https',
      disableStats: true,
      enabledTransports: ['ws', 'wss'],
      scheme: REVERB_SCHEME,
    });
    echoRef.current = echo;
    const channel = echo.channel('audio.' + sessionId);
    channel.listen('AudioStreamReceived', (data: any) => {
      if (typeof data?.transcription === 'string') {
        setStreamTranscription(data.transcription);
      } else {
        setStreamTranscription('');
      }
      if (data?.error) {
        console.error('WebSocket event error:', data.error);
      }
    });
    if (echo.connector && echo.connector.socket) {
      echo.connector.socket.on('connect_error', (err: any) => {
        console.error('WebSocket bağlantı hatası:', err);
      });
    }
    return () => {
      echo.leave('audio.' + sessionId);
      echoRef.current = null;
    };
  }, [isStreaming, sessionId]);

  return (
    <div className="voice-chatbot-layout">
      <div className="left-panel">
        <div className="conversation-history">
          <ConversationHistory
            refreshTrigger={refreshTrigger}
            currentSessionId={sessionId}
            onConversationSelect={handleConversationSelect}
            onNewConversation={handleNewConversation}
          />
        </div>
        <div className="voice-panel-container">
          <VoicePanel
            isRecording={isRecording}
            isProcessing={isProcessing}
            audioStream={streamRef.current}
            startRecording={startRecording}
            stopRecording={stopRecording}
            cancelRecording={cancelRecording}
          />
        </div>
      </div>
      <div className="main-panel">
        <ChatPanel
          messages={messages}
          isProcessing={isProcessing}
          streamTranscription={streamTranscription}
          isStreaming={isStreaming}
          onInputFocusChange={setIsInputFocused}
          onSendMessage={sendTextMessage}
        />
      </div>
    </div>
  );
};

export default VoiceChatbot;

