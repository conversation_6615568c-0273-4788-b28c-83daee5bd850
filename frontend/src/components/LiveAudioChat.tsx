'use client';

import { useState, useEffect, useRef } from 'react';
import { liveAudioStreamer } from '@/utils/liveAudioStreaming';

interface LiveAudioChatProps {
  className?: string;
}

export default function LiveAudioChat({ className = '' }: LiveAudioChatProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingStatus, setStreamingStatus] = useState('');
  const [chunksReceived, setChunksReceived] = useState(0);
  const [sessionId, setSessionId] = useState<string>('');
  const [transcription, setTranscription] = useState('');
  const [response, setResponse] = useState('');

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);

  useEffect(() => {
    // Generate session ID
    const newSessionId = 'live_session_' + Date.now();
    setSessionId(newSessionId);

    // Setup event listeners for live streaming
    const handleStreamStarted = (event: any) => {
      setStreamingStatus('🎵 Audio streaming started...');
      setIsStreaming(true);
      setChunksReceived(0);
    };

    const handleChunkReceived = (event: any) => {
      setChunksReceived(prev => prev + 1);
      setStreamingStatus(`📦 Receiving audio chunks... (${event.detail.chunkNumber})`);
    };

    const handleStreamCompleted = (event: any) => {
      setStreamingStatus('✅ Audio streaming completed!');
      setIsStreaming(false);
    };

    const handleStreamError = (event: any) => {
      setStreamingStatus(`❌ Streaming error: ${event.detail.error}`);
      setIsStreaming(false);
    };

    window.addEventListener('audioStreamStarted', handleStreamStarted);
    window.addEventListener('audioChunkReceived', handleChunkReceived);
    window.addEventListener('audioStreamCompleted', handleStreamCompleted);
    window.addEventListener('audioStreamError', handleStreamError);

    return () => {
      window.removeEventListener('audioStreamStarted', handleStreamStarted);
      window.removeEventListener('audioChunkReceived', handleChunkReceived);
      window.removeEventListener('audioStreamCompleted', handleStreamCompleted);
      window.removeEventListener('audioStreamError', handleStreamError);
      
      // Cleanup streaming session
      liveAudioStreamer.stopSession();
    };
  }, []);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await processAudioLive(audioBlob);
      };

      mediaRecorder.start();
      setIsRecording(true);
      setStreamingStatus('🎤 Recording...');

    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Mikrofon erişimi başarısız!');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setStreamingStatus('⏹️ Recording stopped, processing...');
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
  };

  const processAudioLive = async (audioBlob: Blob) => {
    try {
      setStreamingStatus('🔄 Processing audio...');
      
      // Start live streaming session
      liveAudioStreamer.startSession(sessionId);

      // Send audio for live streaming
      const result = await liveAudioStreamer.sendAudioForLiveStreaming(audioBlob, sessionId);

      if (result.success) {
        setTranscription(result.transcription);
        setResponse(result.response);
        setStreamingStatus('🎵 Live audio streaming started...');
      } else {
        throw new Error(result.error || 'Processing failed');
      }

    } catch (error: any) {
      console.error('Error processing audio:', error);
      setStreamingStatus(`❌ Error: ${error.message}`);
    }
  };

  const stopStreaming = () => {
    liveAudioStreamer.stopSession();
    setIsStreaming(false);
    setStreamingStatus('🔌 Streaming stopped');
  };

  return (
    <div className={`live-audio-chat ${className}`}>
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
        <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
          🎵 Live Audio Streaming Chat
        </h2>

        {/* Session Info */}
        <div className="mb-4 p-3 bg-gray-100 rounded-lg">
          <p className="text-sm text-gray-600">
            <strong>Session ID:</strong> {sessionId}
          </p>
          <p className="text-sm text-gray-600">
            <strong>Chunks Received:</strong> {chunksReceived}
          </p>
        </div>

        {/* Status */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <p className="text-center text-blue-800 font-medium">
            {streamingStatus || '🎤 Ready to record'}
          </p>
        </div>

        {/* Recording Controls */}
        <div className="flex justify-center gap-4 mb-6">
          {!isRecording ? (
            <button
              onClick={startRecording}
              disabled={isStreaming}
              className="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              🎤 Start Recording
            </button>
          ) : (
            <button
              onClick={stopRecording}
              className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 flex items-center gap-2"
            >
              ⏹️ Stop Recording
            </button>
          )}

          {isStreaming && (
            <button
              onClick={stopStreaming}
              className="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 flex items-center gap-2"
            >
              🔌 Stop Streaming
            </button>
          )}
        </div>

        {/* Transcription */}
        {transcription && (
          <div className="mb-4 p-4 bg-green-50 rounded-lg">
            <h3 className="font-semibold text-green-800 mb-2">👤 Your Message:</h3>
            <p className="text-green-700">{transcription}</p>
          </div>
        )}

        {/* Response */}
        {response && (
          <div className="mb-4 p-4 bg-purple-50 rounded-lg">
            <h3 className="font-semibold text-purple-800 mb-2">🤖 AI Response:</h3>
            <p className="text-purple-700">{response}</p>
          </div>
        )}

        {/* Live Streaming Info */}
        <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">ℹ️ Live Streaming Info:</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Audio chunks are streamed in real-time via WebSocket</li>
            <li>• No MP3 file is saved - audio plays as it's generated</li>
            <li>• Uses Laravel Reverb for real-time communication</li>
            <li>• Much faster than traditional file-based approach</li>
          </ul>
        </div>

        {/* Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 p-3 bg-gray-100 rounded-lg">
            <h4 className="font-semibold text-gray-800 mb-2">🔧 Debug Info:</h4>
            <pre className="text-xs text-gray-600 overflow-auto">
              {JSON.stringify({
                isRecording,
                isStreaming,
                chunksReceived,
                sessionId,
                hasTranscription: !!transcription,
                hasResponse: !!response
              }, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
